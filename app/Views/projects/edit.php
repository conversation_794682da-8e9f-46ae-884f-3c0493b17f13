<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Page Header -->
            <div class="page-header-create">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0">
                            <i class="fas fa-edit me-3"></i>
                            Edit Project: <?= esc($project['project_name']) ?>
                        </h1>
                    </div>
                    <div>
                        <a href="/projects" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                        <a href="/projects/view/<?= $project['id'] ?>" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Project Edit Form -->
            <div class="creation-form-container">
                <?php if (session('errors')): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if (session('error')): ?>
                <div class="alert alert-danger">
                    <?= session('error') ?>
                </div>
                <?php endif; ?>

                <form id="editProjectForm" method="POST" action="/projects/update/<?= $project['id'] ?>">
                    <?= csrf_field() ?>

                    <!-- Progress Steps -->
                    <div class="steps-container">
                        <div class="step-item active" data-step="1">
                            <div class="step-content">
                                <span class="step-number">1</span>
                                <span class="step-label">Project Details</span>
                            </div>
                        </div>
                        <div class="step-line"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-content">
                                <span class="step-number">2</span>
                                <span class="step-label">Task Management</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Project Details -->
                    <div class="form-section active" data-step="1">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_id" class="form-label">Project ID</label>
                                    <input type="text" class="form-control" id="project_id"
                                           value="<?= esc($project['project_id']) ?>" readonly>
                                    <small class="text-muted">Project ID cannot be changed</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_name" class="form-label required">Project Name</label>
                                    <input type="text" class="form-control" id="project_name" name="project_name"
                                           value="<?= esc(old('project_name', $project['project_name'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date" class="form-label required">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="<?= esc(old('start_date', $project['start_date'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="client_mobile" class="form-label">Client Mobile</label>
                                    <input type="tel" class="form-control" id="client_mobile" name="client_mobile"
                                           value="<?= esc(old('client_mobile', $project['client_mobile'])) ?>"
                                           placeholder="+91 98765 43210">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Project Location</label>
                                    <input type="text" class="form-control" id="location" name="location"
                                           value="<?= esc(old('location', $project['location'])) ?>"
                                           placeholder="https://maps.google.com/maps?q=location">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Project Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Detailed project description..."><?= esc(old('description', $project['description'])) ?></textarea>
                        </div>
                    </div>

                    <!-- Step 2: Task Management -->
                    <div class="form-section" data-step="2">
                        <div id="existing-tasks">
                            <!-- Existing tasks will be loaded here -->
                        </div>

                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-primary" id="add-new-task">
                                <i class="fas fa-plus me-2"></i>
                                Add New Task
                            </button>
                        </div>
                    </div>

                    <!-- Form Navigation -->
                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>
                            Previous
                        </button>
                        <div class="nav-spacer"></div>
                        <div class="created-info">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Created: <?= date('M d, Y H:i', strtotime($project['created_at'])) ?>
                            </small>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/projects'">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                            <i class="fas fa-save me-2"></i>
                            Update Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 2;

    // Initialize form
    initializeForm();
    loadExistingTasks();

    function initializeForm() {
        updateStepDisplay();

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', nextStep);
        document.getElementById('prev-btn').addEventListener('click', prevStep);

        // Form validation
        const form = document.getElementById('editProjectForm');
        form.addEventListener('submit', handleFormSubmit);
    }

    function updateStepDisplay() {
        // Update step indicators
        document.querySelectorAll('.step-item').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.toggle('active', stepNumber === currentStep);
            step.classList.toggle('completed', stepNumber < currentStep);
        });

        // Update step lines
        document.querySelectorAll('.step-line').forEach((line, index) => {
            line.classList.toggle('completed', index + 1 < currentStep);
        });

        // Show/hide form sections
        document.querySelectorAll('.form-section').forEach((section, index) => {
            const stepNumber = index + 1;
            section.classList.toggle('active', stepNumber === currentStep);
        });

        // Update navigation buttons
        document.getElementById('prev-btn').style.display = currentStep > 1 ? 'block' : 'none';
        document.getElementById('next-btn').style.display = currentStep < totalSteps ? 'block' : 'none';
        document.getElementById('submit-btn').style.display = currentStep === totalSteps ? 'block' : 'none';
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
        }
    }

    function loadExistingTasks() {
        // Load existing tasks for this project
        fetch(`/projects/getTasks/<?= $project['id'] ?>`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExistingTasks(data.tasks);
                }
            })
            .catch(error => {
                console.error('Error loading tasks:', error);
            });
    }

    function displayExistingTasks(tasks) {
        const container = document.getElementById('existing-tasks');
        if (tasks.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No tasks found for this project.</p>';
            return;
        }

        container.innerHTML = tasks.map(task => `
            <div class="task-edit-card" data-task-id="${task.id}">
                <div class="task-header">
                    <h6 class="task-title">
                        <i class="fas fa-tasks me-2"></i>
                        ${task.task_name}
                    </h6>
                    <div class="task-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary edit-task" data-task-id="${task.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-task" data-task-id="${task.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="task-details">
                    <div class="row">
                        <div class="col-md-3">
                            <small class="text-muted">Assignee:</small><br>
                            <span>${task.assigned_username || 'Unassigned'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Status:</small><br>
                            <span class="badge badge-${task.status}">${task.status.replace('_', ' ')}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Priority:</small><br>
                            <span>${task.priority || 'Medium'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Target Days:</small><br>
                            <span>${task.target_days || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const submitBtn = document.getElementById('submit-btn');
        const spinner = submitBtn.querySelector('.spinner-border');

        // Show loading state
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';

        fetch(e.target.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and redirect
                alert('Project updated successfully!');
                window.location.href = '/projects';
            } else {
                alert('Error: ' + (data.message || 'Failed to update project'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the project');
        })
        .finally(() => {
            // Hide loading state
            submitBtn.disabled = false;
            spinner.style.display = 'none';
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: #dc3545;
}

.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: #28a745;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn {
    border-radius: 0.375rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}
</style>
<?= $this->endSection() ?>
