# SmartFlo Server Configuration

## Server Details
- **Primary IP Address**: ************
- **Secondary IP Address**: ************ (when available)
- **Port**: 8080
- **Primary URL**: http://************:8080
- **Secondary URL**: http://************:8080 (when available)

## Quick Start

### Option 1: Use the startup script
```bash
./start-server.sh
```

### Option 2: Manual start (Primary IP)
```bash
cd "/Users/<USER>/Desktop/aug/SmartFlo 2.0"
php spark serve --host ************ --port 8080
```

### Option 3: Manual start (Secondary IP - when available)
```bash
cd "/Users/<USER>/Desktop/aug/SmartFlo 2.0"
php spark serve --host ************ --port 8080
```

## Application URLs

### Primary Access (************)
- **Home**: http://************:8080
- **Login**: http://************:8080/auth/login
- **Dashboard**: http://************:8080/dashboard
- **Admin Dashboard**: http://************:8080/admin/dashboard
- **User Management**: http://************:8080/admin/users
- **Role Management**: http://************:8080/admin/roles

### Secondary Access (************ - when available)
- **Home**: http://************:8080
- **Login**: http://************:8080/auth/login
- **Dashboard**: http://************:8080/dashboard
- **Admin Dashboard**: http://************:8080/admin/dashboard
- **User Management**: http://************:8080/admin/users
- **Role Management**: http://************:8080/admin/roles

## Default Admin Credentials
- **Username**: admin
- **Password**: Admin@123

## Configuration
- Base URL is configured in `app/Config/App.php`
- Server always binds to ************:8080
- Uses PHP built-in development server

## Features
- ✅ Modern responsive design
- ✅ User management with deactivate functionality
- ✅ Role and permission management
- ✅ Mobile-first interface with bottom navigation
- ✅ PWA capabilities
- ✅ Real-time data loading
- ✅ Secure authentication system

## Network Access
The server is accessible from any device on the local network at:
**http://************:8080**

## Troubleshooting
If the server doesn't start on ************:
1. Check if the IP address is available on your network interface
2. Ensure no other service is using port 8080
3. Try running with sudo if permission issues occur
4. Check firewall settings if accessing from other devices
