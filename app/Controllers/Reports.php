<?php

namespace App\Controllers;

use App\Libraries\AuthLibrary;
use App\Models\ProjectModel;
use App\Models\ProjectTimelineModel;
use App\Models\UserModel;

class Reports extends BaseController
{
    protected $authLib;
    protected $projectModel;
    protected $timelineModel;
    protected $userModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->projectModel = new ProjectModel();
        $this->timelineModel = new ProjectTimelineModel();
        $this->userModel = new UserModel();
    }

    /**
     * Main reports dashboard
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        // Check if user has permission to view reports
        if (!in_array($user['roles'], ['admin', 'manager', 'projects'])) {
            return redirect()->to('/dashboard')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Daily Reports - SmartFlo',
            'user' => $user,
            'page' => 'reports'
        ];

        return view('reports/index', $data);
    }

    /**
     * Debug endpoint to check database tables
     */
    public function debugTables()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON(['error' => 'Not logged in']);
        }

        $user = $this->authLib->user();
        if ($user['roles'] !== 'admin') {
            return $this->response->setJSON(['error' => 'Admin only']);
        }

        $db = \Config\Database::connect();

        $result = [
            'database_connected' => true,
            'tables' => [],
            'project_timeline_exists' => false,
            'projects_count' => 0,
            'users_count' => 0,
            'timeline_count' => 0
        ];

        try {
            // Check if tables exist
            $tables = $db->listTables();
            $result['tables'] = $tables;
            $result['project_timeline_exists'] = in_array('project_timeline', $tables);

            // Count records
            $result['projects_count'] = $this->projectModel->countAll();
            $result['users_count'] = $this->userModel->countAll();

            if ($result['project_timeline_exists']) {
                $result['timeline_count'] = $this->timelineModel->countAll();
            }

        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
            $result['database_connected'] = false;
        }

        return $this->response->setJSON($result);
    }

    /**
     * Get daily report data
     */
    public function getDailyReport()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Check permissions
        if (!in_array($user['roles'], ['admin', 'manager', 'projects'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        try {
            $date = $this->request->getGet('date') ?: date('Y-m-d');
            $staffId = $this->request->getGet('staff_id');

            // Check if timeline table exists
            $db = \Config\Database::connect();
            $tables = $db->listTables();
            $hasTimelineTable = in_array('project_timeline', $tables);

            if ($hasTimelineTable) {
                $reportData = $this->generateDailyReport($date, $staffId);
            } else {
                // Use simplified report without timeline
                $reportData = $this->generateSimplifiedReport($date, $staffId);
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $reportData,
                'has_timeline' => $hasTimelineTable
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error generating daily report: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error generating report: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Simple test endpoint
     */
    public function test()
    {
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Reports controller is working',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get staff list for reports
     */
    public function getStaffList()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        try {
            $staff = $this->userModel->where('is_active', 1)->findAll();

            $staffList = array_map(function($user) {
                return [
                    'id' => $user['id'],
                    'name' => trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: $user['username'],
                    'username' => $user['username'],
                    'role' => $user['roles']
                ];
            }, $staff);

            return $this->response->setJSON([
                'success' => true,
                'staff' => $staffList
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error loading staff list: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading staff list'
            ])->setStatusCode(500);
        }
    }

    /**
     * Generate simplified report without timeline table
     */
    private function generateSimplifiedReport($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            // Get projects updated on this date
            $projectQuery = $this->projectModel
                ->select('projects.*, users.username, users.first_name, users.last_name')
                ->join('users', 'users.id = projects.assigned_to', 'left')
                ->where('projects.updated_at >=', $startDate)
                ->where('projects.updated_at <=', $endDate);

            if ($staffId) {
                $projectQuery->where('projects.assigned_to', $staffId);
            }

            $projects = $projectQuery->orderBy('projects.updated_at', 'DESC')->findAll();

            // Create activities from project updates
            $activities = [];
            foreach ($projects as $project) {
                $activities[] = [
                    'id' => 'project_' . $project['id'],
                    'project_id' => $project['id'],
                    'user_id' => $project['assigned_to'],
                    'action_type' => 'project_update',
                    'title' => 'Project updated: ' . ucwords(str_replace('_', ' ', $project['status'])),
                    'description' => 'Project status updated',
                    'notes' => $project['last_update_notes'] ?? '',
                    'created_at' => $project['updated_at'],
                    'username' => $project['username'],
                    'first_name' => $project['first_name'],
                    'last_name' => $project['last_name'],
                    'project_name' => $project['project_name'],
                    'client_name' => $project['client_name'],
                    'new_status' => $project['status']
                ];
            }

            // Get simplified project stats
            $projectStats = $this->getSimplifiedProjectStats($date, $staffId);

            // Get simplified staff productivity
            $staffProductivity = $this->getSimplifiedStaffProductivity($date, $staffId);

            return [
                'date' => $date,
                'activities' => $activities,
                'project_stats' => $projectStats,
                'staff_productivity' => $staffProductivity,
                'work_durations' => [], // No work durations without timeline
                'summary' => [
                    'total_activities' => count($activities),
                    'unique_projects' => count(array_unique(array_column($activities, 'project_id'))),
                    'total_work_time' => 0,
                    'total_work_time_formatted' => '0 hours',
                    'projects_started' => $projectStats['projects_started'],
                    'projects_completed' => $projectStats['projects_completed'],
                    'average_session_time' => 0
                ]
            ];
        } catch (\Exception $e) {
            log_message('error', 'Simplified report generation failed: ' . $e->getMessage());
            return [
                'date' => $date,
                'activities' => [],
                'project_stats' => [
                    'projects_started' => 0,
                    'projects_completed' => 0,
                    'projects_on_hold' => 0,
                    'projects_resumed' => 0,
                    'total_active_projects' => 0
                ],
                'staff_productivity' => [],
                'work_durations' => [],
                'summary' => [
                    'total_activities' => 0,
                    'unique_projects' => 0,
                    'total_work_time' => 0,
                    'total_work_time_formatted' => '0 hours',
                    'projects_started' => 0,
                    'projects_completed' => 0,
                    'average_session_time' => 0
                ]
            ];
        }
    }

    /**
     * Generate daily report data
     */
    private function generateDailyReport($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            // Get timeline activities for the date with error handling
            $activities = [];
            try {
                $timelineQuery = $this->timelineModel
                    ->select('project_timeline.*, users.username, users.first_name, users.last_name, projects.project_name, projects.client_name')
                    ->join('users', 'users.id = project_timeline.user_id', 'left')
                    ->join('projects', 'projects.id = project_timeline.project_id', 'left')
                    ->where('project_timeline.created_at >=', $startDate)
                    ->where('project_timeline.created_at <=', $endDate);

                if ($staffId) {
                    $timelineQuery->where('project_timeline.user_id', $staffId);
                }

                $activities = $timelineQuery->orderBy('project_timeline.created_at', 'ASC')->findAll();
            } catch (\Exception $e) {
                log_message('error', 'Timeline query failed, using fallback: ' . $e->getMessage());
                $activities = $this->getFallbackActivities($date, $staffId);
            }

            // Get project status summary
            $projectStats = $this->getProjectStats($date, $staffId);

            // Get staff productivity data
            $staffProductivity = $this->getStaffProductivity($date, $staffId);

            // Get work duration summary
            $workDurations = $this->getWorkDurations($date, $staffId);

            return [
                'date' => $date,
                'activities' => $activities,
                'project_stats' => $projectStats,
                'staff_productivity' => $staffProductivity,
                'work_durations' => $workDurations,
                'summary' => $this->generateSummary($activities, $projectStats, $workDurations)
            ];
        } catch (\Exception $e) {
            log_message('error', 'Report generation failed: ' . $e->getMessage());

            // Return minimal report structure
            return [
                'date' => $date,
                'activities' => [],
                'project_stats' => [
                    'projects_started' => 0,
                    'projects_completed' => 0,
                    'projects_on_hold' => 0,
                    'projects_resumed' => 0,
                    'total_active_projects' => 0
                ],
                'staff_productivity' => [],
                'work_durations' => [],
                'summary' => [
                    'total_activities' => 0,
                    'unique_projects' => 0,
                    'total_work_time' => 0,
                    'total_work_time_formatted' => '0 seconds',
                    'projects_started' => 0,
                    'projects_completed' => 0,
                    'average_session_time' => 0
                ]
            ];
        }
    }

    /**
     * Get fallback activities from project updates when timeline is not available
     */
    private function getFallbackActivities($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            // Get projects updated on this date
            $projectQuery = $this->projectModel
                ->select('projects.*, users.username, users.first_name, users.last_name')
                ->join('users', 'users.id = projects.assigned_to', 'left')
                ->where('projects.updated_at >=', $startDate)
                ->where('projects.updated_at <=', $endDate);

            if ($staffId) {
                $projectQuery->where('projects.assigned_to', $staffId);
            }

            $projects = $projectQuery->orderBy('projects.updated_at', 'ASC')->findAll();

            $activities = [];
            foreach ($projects as $project) {
                $activities[] = [
                    'id' => 'project_' . $project['id'],
                    'project_id' => $project['id'],
                    'user_id' => $project['assigned_to'],
                    'action_type' => 'status_change',
                    'old_status' => 'unknown',
                    'new_status' => $project['status'],
                    'title' => 'Project status: ' . ucwords(str_replace('_', ' ', $project['status'])),
                    'description' => 'Project updated',
                    'notes' => $project['last_update_notes'] ?? '',
                    'created_at' => $project['updated_at'],
                    'username' => $project['username'],
                    'first_name' => $project['first_name'],
                    'last_name' => $project['last_name'],
                    'project_name' => $project['project_name'],
                    'client_name' => $project['client_name']
                ];
            }

            return $activities;
        } catch (\Exception $e) {
            log_message('error', 'Fallback activities failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get project statistics for the date
     */
    private function getProjectStats($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            $stats = [
                'projects_started' => 0,
                'projects_completed' => 0,
                'projects_on_hold' => 0,
                'projects_resumed' => 0,
                'total_active_projects' => 0
            ];

            // Try to count status changes from timeline
            try {
                $statusChanges = $this->timelineModel
                    ->where('created_at >=', $startDate)
                    ->where('created_at <=', $endDate);

                if ($staffId) {
                    $statusChanges->where('user_id', $staffId);
                }

                $changes = $statusChanges->findAll();

                foreach ($changes as $change) {
                    switch ($change['new_status']) {
                        case 'in_progress':
                            if ($change['old_status'] === 'not_started' || $change['old_status'] === 'planning') {
                                $stats['projects_started']++;
                            } elseif ($change['old_status'] === 'on_hold') {
                                $stats['projects_resumed']++;
                            }
                            break;
                        case 'completed':
                            $stats['projects_completed']++;
                            break;
                        case 'on_hold':
                            $stats['projects_on_hold']++;
                            break;
                    }
                }
            } catch (\Exception $e) {
                log_message('error', 'Timeline stats query failed: ' . $e->getMessage());
                // Use fallback method - count projects by status updated today
                $projectsUpdatedToday = $this->projectModel
                    ->where('updated_at >=', $startDate)
                    ->where('updated_at <=', $endDate);

                if ($staffId) {
                    $projectsUpdatedToday->where('assigned_to', $staffId);
                }

                $updatedProjects = $projectsUpdatedToday->findAll();

                foreach ($updatedProjects as $project) {
                    switch ($project['status']) {
                        case 'in_progress':
                            $stats['projects_started']++;
                            break;
                        case 'completed':
                            $stats['projects_completed']++;
                            break;
                        case 'on_hold':
                            $stats['projects_on_hold']++;
                            break;
                    }
                }
            }

            // Get total active projects
            try {
                $activeProjects = $this->projectModel
                    ->whereIn('status', ['in_progress', 'planning', 'on_hold']);

                if ($staffId) {
                    $activeProjects->where('assigned_to', $staffId);
                }

                $stats['total_active_projects'] = $activeProjects->countAllResults();
            } catch (\Exception $e) {
                log_message('error', 'Active projects count failed: ' . $e->getMessage());
                $stats['total_active_projects'] = 0;
            }

            return $stats;
        } catch (\Exception $e) {
            log_message('error', 'Project stats generation failed: ' . $e->getMessage());
            return [
                'projects_started' => 0,
                'projects_completed' => 0,
                'projects_on_hold' => 0,
                'projects_resumed' => 0,
                'total_active_projects' => 0
            ];
        }
    }

    /**
     * Get staff productivity data
     */
    private function getStaffProductivity($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            $productivity = [];
            try {
                $query = $this->timelineModel
                    ->select('users.id, users.username, users.first_name, users.last_name, COUNT(*) as activity_count')
                    ->join('users', 'users.id = project_timeline.user_id', 'left')
                    ->where('project_timeline.created_at >=', $startDate)
                    ->where('project_timeline.created_at <=', $endDate)
                    ->groupBy('users.id');

                if ($staffId) {
                    $query->where('users.id', $staffId);
                }

                $productivity = $query->findAll();
            } catch (\Exception $e) {
                log_message('error', 'Staff productivity timeline query failed: ' . $e->getMessage());
                // Fallback: get staff from user table and count project updates
                $staffQuery = $this->userModel->where('is_active', 1);
                if ($staffId) {
                    $staffQuery->where('id', $staffId);
                }
                $staff = $staffQuery->findAll();

                foreach ($staff as $user) {
                    $activityCount = $this->projectModel
                        ->where('assigned_to', $user['id'])
                        ->where('updated_at >=', $startDate)
                        ->where('updated_at <=', $endDate)
                        ->countAllResults();

                    $productivity[] = [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'first_name' => $user['first_name'],
                        'last_name' => $user['last_name'],
                        'activity_count' => $activityCount
                    ];
                }
            }

            return array_map(function($staff) {
                return [
                    'id' => $staff['id'],
                    'name' => trim(($staff['first_name'] ?? '') . ' ' . ($staff['last_name'] ?? '')) ?: $staff['username'],
                    'username' => $staff['username'],
                    'activity_count' => $staff['activity_count'] ?? 0
                ];
            }, $productivity);
        } catch (\Exception $e) {
            log_message('error', 'Staff productivity generation failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get work duration data
     */
    private function getWorkDurations($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            $workSessions = [];
            try {
                $query = $this->timelineModel
                    ->select('project_timeline.*, users.username, projects.project_name')
                    ->join('users', 'users.id = project_timeline.user_id', 'left')
                    ->join('projects', 'projects.id = project_timeline.project_id', 'left')
                    ->where('project_timeline.created_at >=', $startDate)
                    ->where('project_timeline.created_at <=', $endDate)
                    ->where('project_timeline.old_status', 'in_progress');

                if ($staffId) {
                    $query->where('project_timeline.user_id', $staffId);
                }

                $durations = $query->findAll();

                foreach ($durations as $duration) {
                    $metadata = json_decode($duration['metadata'], true) ?? [];
                    if (isset($metadata['duration_seconds'])) {
                        $workSessions[] = [
                            'user' => $duration['username'],
                            'project' => $duration['project_name'],
                            'duration_seconds' => $metadata['duration_seconds'],
                            'duration_formatted' => $metadata['duration_formatted'] ?? $this->formatDuration($metadata['duration_seconds']),
                            'start_time' => $metadata['start_time'] ?? null,
                            'end_time' => $metadata['end_time'] ?? null
                        ];
                    }
                }
            } catch (\Exception $e) {
                log_message('error', 'Work durations query failed: ' . $e->getMessage());
                // No fallback for work durations as this requires timeline metadata
                $workSessions = [];
            }

            return $workSessions;
        } catch (\Exception $e) {
            log_message('error', 'Work durations generation failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate report summary
     */
    private function generateSummary($activities, $projectStats, $workDurations)
    {
        $totalWorkTime = array_sum(array_column($workDurations, 'duration_seconds'));
        $totalActivities = count($activities);
        $uniqueProjects = count(array_unique(array_column($activities, 'project_id')));

        return [
            'total_activities' => $totalActivities,
            'unique_projects' => $uniqueProjects,
            'total_work_time' => $totalWorkTime,
            'total_work_time_formatted' => $this->formatDuration($totalWorkTime),
            'projects_started' => $projectStats['projects_started'],
            'projects_completed' => $projectStats['projects_completed'],
            'average_session_time' => count($workDurations) > 0 ? $totalWorkTime / count($workDurations) : 0
        ];
    }

    /**
     * Get simplified project stats without timeline
     */
    private function getSimplifiedProjectStats($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            $stats = [
                'projects_started' => 0,
                'projects_completed' => 0,
                'projects_on_hold' => 0,
                'projects_resumed' => 0,
                'total_active_projects' => 0
            ];

            // Count projects by status updated today
            $projectsUpdatedToday = $this->projectModel
                ->where('updated_at >=', $startDate)
                ->where('updated_at <=', $endDate);

            if ($staffId) {
                $projectsUpdatedToday->where('assigned_to', $staffId);
            }

            $updatedProjects = $projectsUpdatedToday->findAll();

            foreach ($updatedProjects as $project) {
                switch ($project['status']) {
                    case 'in_progress':
                        $stats['projects_started']++;
                        break;
                    case 'completed':
                        $stats['projects_completed']++;
                        break;
                    case 'on_hold':
                        $stats['projects_on_hold']++;
                        break;
                }
            }

            // Get total active projects
            $activeProjects = $this->projectModel
                ->whereIn('status', ['in_progress', 'planning', 'on_hold']);

            if ($staffId) {
                $activeProjects->where('assigned_to', $staffId);
            }

            $stats['total_active_projects'] = $activeProjects->countAllResults();

            return $stats;
        } catch (\Exception $e) {
            log_message('error', 'Simplified project stats failed: ' . $e->getMessage());
            return [
                'projects_started' => 0,
                'projects_completed' => 0,
                'projects_on_hold' => 0,
                'projects_resumed' => 0,
                'total_active_projects' => 0
            ];
        }
    }

    /**
     * Get simplified staff productivity without timeline
     */
    private function getSimplifiedStaffProductivity($date, $staffId = null)
    {
        try {
            $startDate = $date . ' 00:00:00';
            $endDate = $date . ' 23:59:59';

            $staffQuery = $this->userModel->where('is_active', 1);
            if ($staffId) {
                $staffQuery->where('id', $staffId);
            }
            $staff = $staffQuery->findAll();

            $productivity = [];
            foreach ($staff as $user) {
                $activityCount = $this->projectModel
                    ->where('assigned_to', $user['id'])
                    ->where('updated_at >=', $startDate)
                    ->where('updated_at <=', $endDate)
                    ->countAllResults();

                $productivity[] = [
                    'id' => $user['id'],
                    'name' => trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: $user['username'],
                    'username' => $user['username'],
                    'activity_count' => $activityCount
                ];
            }

            return $productivity;
        } catch (\Exception $e) {
            log_message('error', 'Simplified staff productivity failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Format duration in seconds to readable format
     */
    private function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return $seconds . 's';
        } elseif ($seconds < 3600) {
            return floor($seconds / 60) . 'm ' . ($seconds % 60) . 's';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . 'h ' . $minutes . 'm';
        }
    }
}
