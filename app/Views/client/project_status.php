<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .client-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .project-header {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-not_started { background: #6c757d; color: white; }
        .status-in_progress { background: #0d6efd; color: white; }
        .status-on_hold { background: #fd7e14; color: white; }
        .status-completed { background: #198754; color: white; }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin: 0 auto;
        }
        
        .task-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #e9ecef;
        }
        
        .task-card.task-completed { border-left-color: #198754; opacity: 0.8; }
        .task-card.task-in_progress { border-left-color: #0d6efd; }
        .task-card.task-on_hold { border-left-color: #fd7e14; }
        .task-card.task-not_started { border-left-color: #6c757d; }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #0d6efd;
        }
        
        .last-update {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .smartflo-logo {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #0d6efd;
            color: white;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            background: #0a58ca;
        }
    </style>
</head>
<body>
    <div class="client-container">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <a href="<?= base_url() ?>" class="smartflo-logo">
                <i class="fas fa-building me-2"></i>
                SmartFlo
            </a>
            <div class="text-white">
                <i class="fas fa-clock me-2"></i>
                Last updated: <span id="lastUpdated"><?= date('M j, Y g:i A') ?></span>
            </div>
        </div>

        <!-- Project Header -->
        <div class="project-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge bg-secondary me-3"><?= $project['project_id'] ?></span>
                        <span class="status-badge status-<?= $project['status'] ?>"><?= ucwords(str_replace('_', ' ', $project['status'])) ?></span>
                    </div>
                    <h1 class="mb-2"><?= esc($project['project_name']) ?></h1>
                    <p class="text-muted mb-2">
                        <i class="fas fa-user me-2"></i>
                        <?= esc($project['client_name']) ?>
                    </p>
                    <p class="text-muted mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?= esc($project['location']) ?>
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <div class="progress-circle" style="background: conic-gradient(#0d6efd <?= ($project['progress_percentage'] / 100) * 360 ?>deg, #e9ecef <?= ($project['progress_percentage'] / 100) * 360 ?>deg);">
                        <div style="background: white; width: 90px; height: 90px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #495057;">
                            <?= $project['progress_percentage'] ?>%
                        </div>
                    </div>
                    <small class="text-muted mt-2 d-block">Overall Progress</small>
                </div>
            </div>
            
            <?php if ($project['description']): ?>
            <div class="mt-4 pt-4 border-top">
                <h6>Project Description</h6>
                <p class="text-muted"><?= esc($project['description']) ?></p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number"><?= $stats['total_tasks'] ?></div>
                    <div class="text-muted">Total Tasks</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-primary"><?= $stats['in_progress'] ?></div>
                    <div class="text-muted">In Progress</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success"><?= $stats['completed'] ?></div>
                    <div class="text-muted">Completed</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning"><?= $stats['on_hold'] ?></div>
                    <div class="text-muted">On Hold</div>
                </div>
            </div>
        </div>

        <!-- Project Timeline -->
        <div class="row">
            <div class="col-md-6">
                <div class="stats-card text-start">
                    <h6 class="mb-3">
                        <i class="fas fa-calendar me-2"></i>
                        Project Timeline
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Start Date</small>
                            <div class="fw-bold"><?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Target Completion</small>
                            <div class="fw-bold"><?= date('M j, Y', strtotime($project['target_completion'])) ?></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <?php if ($project['last_update_notes']): ?>
                <div class="last-update">
                    <h6 class="mb-2">
                        <i class="fas fa-bell me-2"></i>
                        Latest Update
                    </h6>
                    <p class="mb-0"><?= esc($project['last_update_notes']) ?></p>
                    <?php if ($project['last_updated']): ?>
                    <small class="text-muted">
                        <?= date('M j, Y g:i A', strtotime($project['last_updated'])) ?>
                    </small>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tasks -->
        <div class="mt-4">
            <h4 class="text-white mb-3">
                <i class="fas fa-tasks me-2"></i>
                Project Tasks
            </h4>
            
            <?php if (empty($tasks)): ?>
            <div class="task-card text-center">
                <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">No tasks available</h6>
                <p class="text-muted mb-0">Task information will be updated as the project progresses.</p>
            </div>
            <?php else: ?>
            <?php foreach ($tasks as $task): ?>
            <div class="task-card task-<?= $task['status'] ?>">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0"><?= esc($task['task_name']) ?></h6>
                    <span class="status-badge status-<?= $task['status'] ?>"><?= ucwords(str_replace('_', ' ', $task['status'])) ?></span>
                </div>
                <p class="text-muted mb-2"><?= esc($task['description'] ?: 'No description available') ?></p>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-tag me-1"></i>
                            <?= esc($task['task_type_name']) ?>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if ($task['completed_date']): ?>
                        <small class="text-success">
                            <i class="fas fa-check me-1"></i>
                            Completed: <?= date('M j, Y', strtotime($task['completed_date'])) ?>
                        </small>
                        <?php elseif ($task['due_date']): ?>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                        </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
        
        // Update last updated time
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
