<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Libraries\AuthLibrary;

class TaskManager extends Controller
{
    protected $authLib;
    protected $taskCategoryModel;
    protected $taskModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        // We'll create these models next
        // $this->taskCategoryModel = new \App\Models\TaskCategoryModel();
        // $this->taskModel = new \App\Models\TaskModel();
    }

    /**
     * Task management page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        // Check if user has permission to manage tasks
        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return redirect()->to('/projects')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Task Management',
            'user' => $user,
            'page' => 'task-manager'
        ];

        return view('task_manager/index', $data);
    }

    /**
     * Get task categories and tasks
     */
    public function getTaskData()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // For now, return the hardcoded task data
        // Later we'll replace this with database queries
        $taskData = [
            'office' => [
                ['id' => 'planning', 'name' => 'Project Planning', 'default_days' => 3],
                ['id' => 'documentation', 'name' => 'Documentation', 'default_days' => 2],
                ['id' => 'permits', 'name' => 'Permit Processing', 'default_days' => 7],
                ['id' => 'coordination', 'name' => 'Team Coordination', 'default_days' => 1]
            ],
            'site' => [
                ['id' => 'supervision', 'name' => 'Site Supervision', 'default_days' => 10],
                ['id' => 'quality_control', 'name' => 'Quality Control', 'default_days' => 5],
                ['id' => 'safety', 'name' => 'Safety Management', 'default_days' => 3],
                ['id' => 'material_management', 'name' => 'Material Management', 'default_days' => 2]
            ],
            'design' => [
                ['id' => '3d_design', 'name' => '3D Design', 'default_days' => 14],
                ['id' => 'architectural', 'name' => 'Architectural Design', 'default_days' => 21],
                ['id' => 'structural', 'name' => 'Structural Design', 'default_days' => 14],
                ['id' => 'electrical', 'name' => 'Electrical Design', 'default_days' => 7]
            ],
            'management' => [
                ['id' => 'project_management', 'name' => 'Project Management', 'default_days' => 5],
                ['id' => 'client_relations', 'name' => 'Client Relations', 'default_days' => 2],
                ['id' => 'budget_control', 'name' => 'Budget Control', 'default_days' => 3],
                ['id' => 'timeline_management', 'name' => 'Timeline Management', 'default_days' => 2]
            ]
        ];

        return $this->response->setJSON([
            'success' => true,
            'categories' => array_keys($taskData),
            'tasks' => $taskData
        ]);
    }

    /**
     * Add new task category
     */
    public function addCategory()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $categoryName = $this->request->getPost('category_name');
        $categoryId = $this->request->getPost('category_id');

        if (empty($categoryName) || empty($categoryId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Category name and ID are required'
            ]);
        }

        // TODO: Add to database
        // For now, just return success
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Category added successfully',
            'category' => [
                'id' => $categoryId,
                'name' => $categoryName
            ]
        ]);
    }

    /**
     * Add new task to category
     */
    public function addTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $categoryId = $this->request->getPost('category_id');
        $taskName = $this->request->getPost('task_name');
        $taskId = $this->request->getPost('task_id');
        $defaultDays = $this->request->getPost('default_days');

        if (empty($categoryId) || empty($taskName) || empty($taskId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Category ID, task name, and task ID are required'
            ]);
        }

        // TODO: Add to database
        // For now, just return success
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Task added successfully',
            'task' => [
                'id' => $taskId,
                'name' => $taskName,
                'category_id' => $categoryId,
                'default_days' => (int)$defaultDays ?: 7
            ]
        ]);
    }

    /**
     * Update task
     */
    public function updateTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $taskId = $this->request->getPost('task_id');
        $taskName = $this->request->getPost('task_name');
        $defaultDays = $this->request->getPost('default_days');

        if (empty($taskId) || empty($taskName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task ID and name are required'
            ]);
        }

        // TODO: Update in database
        // For now, just return success
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Task updated successfully',
            'task' => [
                'id' => $taskId,
                'name' => $taskName,
                'default_days' => (int)$defaultDays ?: 7
            ]
        ]);
    }

    /**
     * Delete task
     */
    public function deleteTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $taskId = $this->request->getPost('task_id');

        if (empty($taskId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task ID is required'
            ]);
        }

        // TODO: Delete from database
        // For now, just return success
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Task deleted successfully'
        ]);
    }
}
