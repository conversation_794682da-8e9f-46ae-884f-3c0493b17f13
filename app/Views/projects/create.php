<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Page Header -->
            <div class="page-header-create">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0">
                            <i class="fas fa-plus-circle me-3"></i>
                            Create New Project
                        </h1>
                    </div>
                    <div>
                        <a href="/projects" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                    </div>
                </div>
            </div>

            <!-- Project Creation Form -->
            <div class="creation-form-container">
                <form id="projectCreationForm" method="POST" action="/projects/create">
                    <?= csrf_field() ?>
                    
                    <!-- Progress Steps -->
                    <div class="steps-container">
                        <div class="step-item active" data-step="1">
                            <div class="step-content">
                                <span class="step-number">1</span>
                                <span class="step-label">Project Details</span>
                            </div>
                        </div>
                        <div class="step-line"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-content">
                                <span class="step-number">2</span>
                                <span class="step-label">Task Assignment</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Project Details -->
                    <div class="form-section active" data-step="1">


                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_id" class="form-label required">Project ID</label>
                                    <input type="text" class="form-control" id="project_id" name="project_id"
                                           placeholder="PRJ-001" required pattern="[a-zA-Z0-9\-_.]+">
                                    <div class="invalid-feedback"></div>
                                    <div class="valid-feedback">Project ID is available</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_name" class="form-label required">Project Name</label>
                                    <input type="text" class="form-control" id="project_name" name="project_name"
                                           placeholder="Project Name as in folder name" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date" class="form-label required">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= date('Y-m-d') ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="client_mobile" class="form-label">Client Mobile</label>
                                    <input type="tel" class="form-control" id="client_mobile" name="client_mobile"
                                           placeholder="+91 98765 43210" value="+91 ">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Project Location</label>
                                    <input type="text" class="form-control" id="location" name="location"
                                           placeholder="https://maps.google.com/maps?q=location">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Project Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Detailed project description..."></textarea>
                        </div>
                    </div>

                    <!-- Step 2: Assignment -->
                    <div class="form-section" data-step="2">


                        <div id="task-assignments">
                            <!-- Primary Assignment -->
                            <div class="assignment-row" data-assignment="0">
                                <div class="assignment-card">
                                    <div class="assignment-header">
                                        <h6 class="assignment-title">
                                            <i class="fas fa-tasks me-2"></i>
                                            Task #1
                                        </h6>
                                        <button type="button" class="btn btn-xs btn-outline-danger remove-assignment" style="display: none;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact required">Task Category</label>
                                                <select class="form-control-compact task-category" name="task_category[]" required>
                                                    <option value="">Select Category</option>
                                                    <option value="office">Office</option>
                                                    <option value="site">Site</option>
                                                    <option value="design">Design</option>
                                                    <option value="management">Management</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact required">Task</label>
                                                <select class="form-control-compact task-type" name="task_type[]" required>
                                                    <option value="">Select Task</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact required">Assignee</label>
                                                <select class="form-control-compact assignee-select" name="assigned_to[]" required>
                                                    <option value="">Loading users...</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact required">Target Days</label>
                                                <input type="number" class="form-control-compact target-days" name="target_days[]"
                                                       min="1" max="365" value="7" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact">Depends On</label>
                                                <select class="form-control-compact depends-on" name="depends_on[]" multiple>
                                                    <option value="">No dependency (can start immediately)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group-compact">
                                                <label class="form-label-compact">Priority</label>
                                                <select class="form-control-compact task-priority" name="task_priority[]">
                                                    <option value="medium">Medium</option>
                                                    <option value="low">Low</option>
                                                    <option value="high">High</option>
                                                    <option value="urgent">Urgent</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add More Assignment Button -->
                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-primary" id="add-assignment">
                                <i class="fas fa-plus me-2"></i>
                                Add More Assignment
                            </button>
                        </div>

                        <!-- Project Duration Summary -->
                        <div class="duration-summary-card-horizontal">
                            <div class="duration-header-horizontal">
                                <i class="fas fa-clock me-2"></i>
                                <span class="duration-title">Project Duration Summary</span>
                            </div>
                            <div class="duration-content-horizontal">
                                <div class="duration-item-horizontal">
                                    <span class="duration-label-horizontal">Total Days:</span>
                                    <span class="duration-value-horizontal" id="totalDays">7</span>
                                </div>
                                <div class="duration-separator">|</div>
                                <div class="duration-item-horizontal">
                                    <span class="duration-label-horizontal">Critical Path:</span>
                                    <span class="duration-value-horizontal" id="criticalPath">7</span>
                                </div>
                                <div class="duration-separator">|</div>
                                <div class="duration-item-horizontal">
                                    <span class="duration-label-horizontal">Completion:</span>
                                    <span class="duration-value-horizontal" id="estimatedCompletion">-</span>
                                </div>
                            </div>
                        </div>




                    </div>

                    <!-- Form Navigation -->
                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>
                            Previous
                        </button>
                        <div class="nav-spacer"></div>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/projects'">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                            <i class="fas fa-rocket me-2"></i>
                            Create Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-5">
                <div class="success-icon">
                    <i class="fas fa-check-circle fa-4x text-success"></i>
                </div>
                <h4 class="mt-3 mb-2">Project Created Successfully!</h4>
                <p class="text-muted mb-4">Your new project has been created and the assigned user has been notified.</p>
                <div class="d-grid gap-2">
                    <a href="/projects" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        View All Projects
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/projects/create'">
                        <i class="fas fa-plus me-2"></i>
                        Create Another Project
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Page-based Project Creation Styles */
.page-header-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    line-height: 1.2;
}

.btn-outline-secondary {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

.btn-outline-secondary:hover {
    color: #667eea !important;
    background: white !important;
    border-color: white !important;
}

.page-subtitle {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.creation-form-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Progress Steps */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.step-item {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.step-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #cbd5e1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.step-item.active .step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.step-item.completed .step-number {
    background: #10b981;
}

.step-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    white-space: nowrap;
}

.step-item.active .step-label {
    color: #667eea;
}

.step-line {
    width: 80px;
    height: 2px;
    background: #e2e8f0;
    margin: 0 1rem;
}

.step-line.completed {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Form Sections */
.form-section {
    display: none;
    padding: 1.5rem;
}

.form-section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header-compact {
    text-align: center;
    margin-bottom: 1rem;
}

.section-header h3,
.section-header-compact h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.section-header-compact h3 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.section-header p,
.section-header-compact p {
    font-size: 1rem;
    color: #64748b;
    margin: 0;
}

.section-header-compact p {
    font-size: 0.875rem;
}

.section-header-single-line {
    text-align: center;
    margin-bottom: 1rem;
}

.section-header-single-line h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.section-subtitle {
    font-size: 0.875rem;
    font-weight: 400;
    color: #64748b;
}

/* Form Groups */
.form-group {
    margin-bottom: 2rem;
}

.form-group-compact {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.form-label-compact {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    display: block;
    font-size: 0.875rem;
}

.form-label.required::after,
.form-label-compact.required::after {
    content: ' *';
    color: #ef4444;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-control-compact {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-control:focus,
.form-control-compact:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    outline: none;
}

.form-help {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

/* Multiple select styling */
.depends-on[multiple] {
    min-height: 80px;
    background-image: none;
}

.depends-on[multiple] option {
    padding: 4px 8px;
    margin: 1px 0;
}

.depends-on[multiple] option:checked {
    background: #007bff;
    color: white;
}

/* Required field indicator */
.form-label.required::after {
    content: " *";
    color: #dc3545;
}

/* Extra small button */
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.25rem;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #ef4444;
    margin-top: 0.5rem;
}

/* Info Cards */
.info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-top: 2rem;
}

.info-card.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.info-card i {
    color: #667eea;
    flex-shrink: 0;
}

.info-card.success i {
    color: #10b981;
}

.info-card h6 {
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.25rem 0;
}

.info-card p {
    color: #64748b;
    margin: 0;
    font-size: 0.875rem;
}

/* Phase Targets Section */
.phase-targets-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
}

.phase-header {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.phase-header i {
    color: #667eea;
}

.phase-subtitle {
    color: #64748b;
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

.phase-timeline-preview {
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-size: 0.85rem;
    color: #64748b;
}

.phase-timeline-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.phase-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.phase-item i {
    color: #667eea;
    width: 16px;
    text-align: center;
}

.phase-name {
    font-weight: 600;
    color: #374151;
    min-width: 100px;
}

.phase-date {
    color: #6b7280;
    font-size: 0.8rem;
    margin-left: auto;
}

/* User Preview */
.preview-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.user-name {
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.25rem 0;
}

.user-role {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
}

/* Form Navigation */
.form-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem 3rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.nav-spacer {
    flex: 1;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.btn-outline-secondary:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

/* Success Modal */
.success-icon {
    margin-bottom: 1rem;
}

/* Assignment Cards */
.assignment-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.assignment-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.assignment-title {
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
}

.assignment-title i {
    color: #667eea;
}

/* Horizontal Duration Summary */
.duration-summary-card-horizontal {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
}

.duration-header-horizontal {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    color: #1e293b;
    font-weight: 600;
    font-size: 0.95rem;
}

.duration-content-horizontal {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.duration-item-horizontal {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.duration-label-horizontal {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.duration-value-horizontal {
    font-size: 0.875rem;
    font-weight: 700;
    color: #1e293b;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    min-width: 2rem;
    text-align: center;
}

.duration-separator {
    color: #cbd5e1;
    font-weight: 300;
    font-size: 1rem;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .page-header-create {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .page-title {
        font-size: 1.25rem;
    }

    .btn-outline-secondary {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .steps-container {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .step-line {
        width: 2px;
        height: 20px;
        margin: 0.25rem 0;
    }

    .form-section {
        padding: 1rem;
    }

    .row {
        margin-left: 0;
        margin-right: 0;
    }

    .row > [class*="col-"] {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .col-md-4,
    .col-md-6,
    .col-md-3 {
        margin-bottom: 1rem;
    }

    .assignment-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .assignment-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    .assignment-title {
        font-size: 0.9rem;
    }

    .duration-content-horizontal {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .duration-item-horizontal {
        justify-content: space-between;
        padding: 0.5rem;
        background: white;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

    .duration-separator {
        display: none;
    }

    .form-navigation {
        padding: 1rem;
        gap: 0.5rem;
    }

    .form-navigation .btn {
        flex: 1;
        min-width: auto;
    }
}

/* Tablet Responsive Design */
@media (min-width: 769px) and (max-width: 1024px) {
    .form-section {
        padding: 1.25rem;
    }

    .duration-content-horizontal {
        gap: 0.75rem;
    }
}

/* Duration Summary Card */
.duration-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 2rem;
    color: white;
}

.duration-header h6 {
    color: white;
    font-weight: 600;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
}

.duration-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.duration-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.duration-label {
    font-weight: 500;
    opacity: 0.9;
}

.duration-value {
    font-weight: 700;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

/* Dependency Indicators */
.dependency-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: #fef3c7;
    color: #d97706;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.dependency-indicator.locked {
    background: #fee2e2;
    color: #dc2626;
}

.dependency-indicator.unlocked {
    background: #d1fae5;
    color: #059669;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header-create {
        padding: 1.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .steps-container {
        padding: 1.5rem;
    }

    .step-line {
        width: 40px;
    }

    .form-section {
        padding: 2rem 1.5rem;
    }

    .form-navigation {
        padding: 1.5rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .assignment-card {
        padding: 1rem;
    }

    .duration-content {
        gap: 0.5rem;
    }

    .duration-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
    
    .nav-spacer {
        display: none;
    }
    
    .btn {
        flex: 1;
        min-width: 120px;
    }
}
</style>

<!-- Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// Project Creation Page JavaScript
let currentStep = 1;
const maxSteps = 2;

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing project creation form');

    // Initialize form first
    initializeForm();
    setupEventListeners();

    // Load task data first, then initialize everything else
    loadTaskData();

    // Initialize task assignments with proper timing
    setTimeout(() => {
        console.log('Initializing task assignments and loading users');
        setupTaskAssignments();

        // Initialize duration calculation
        updateDependencyOptions();
        calculateProjectDuration();

        // Debug task categories
        console.log('Available task categories:', Object.keys(tasksByCategory));
        const categorySelects = document.querySelectorAll('.task-category');
        console.log(`Found ${categorySelects.length} category selects`);

        // Load users for all existing assignee selects
        const assigneeSelects = document.querySelectorAll('.assignee-select');
        console.log(`Found ${assigneeSelects.length} assignee selects`);
        assigneeSelects.forEach((select, index) => {
            console.log(`Loading users for assignee select ${index + 1}`);
            loadUsersForSelect(select);
        });
    }, 500); // Increased timeout to allow task data to load
});

function initializeForm() {
    updateStepDisplay();
    updateNavigation();
}

function setupEventListeners() {
    // Navigation buttons
    document.getElementById('next-btn').addEventListener('click', nextStep);
    document.getElementById('prev-btn').addEventListener('click', prevStep);

    // Form submission
    document.getElementById('projectCreationForm').addEventListener('submit', handleSubmit);

    // Date change listeners for completion date calculation
    document.getElementById('start_date').addEventListener('change', function() {
        calculateCompletionDate();
        calculateProjectDuration(); // Also update project duration when start date changes
    });

    // Real-time validation
    setupValidation();

    // Setup mobile number formatting
    setupMobileFormatting();
}

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < maxSteps) {
            currentStep++;
            updateStepDisplay();
            updateNavigation();
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
        updateNavigation();
    }
}

function updateStepDisplay() {
    // Hide all sections
    document.querySelectorAll('.form-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show current section
    document.querySelector(`.form-section[data-step="${currentStep}"]`).classList.add('active');
    
    // Update step indicators
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNumber = index + 1;
        item.classList.remove('active', 'completed');
        
        if (stepNumber === currentStep) {
            item.classList.add('active');
        } else if (stepNumber < currentStep) {
            item.classList.add('completed');
        }
    });
    
    // Update step lines
    document.querySelectorAll('.step-line').forEach((line, index) => {
        line.classList.remove('completed');
        if (index + 1 < currentStep) {
            line.classList.add('completed');
        }
    });
}

function updateNavigation() {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    
    // Previous button
    prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';
    
    // Next/Submit buttons
    if (currentStep === maxSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-flex';
    } else {
        nextBtn.style.display = 'inline-flex';
        submitBtn.style.display = 'none';
    }
}

function validateCurrentStep() {
    const currentSection = document.querySelector(`.form-section[data-step="${currentStep}"]`);
    const requiredFields = currentSection.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    // Clear previous validation
    field.classList.remove('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    if (!value && field.required) {
        isValid = false;
        message = 'This field is required';
    } else if (field.name === 'project_id' && value) {
        // Alphanumeric validation for project ID (matching controller pattern)
        if (!/^[a-zA-Z0-9\-_.]+$/.test(value) || value.length < 1) {
            isValid = false;
            message = 'Project ID can only contain letters, numbers, hyphens (-), underscores (_), and periods (.)';
        } else {
            // Check for duplicate project ID in real-time
            checkProjectIdAvailability(value, field);
        }
    } else if (field.name === 'client_mobile' && value) {
        if (!/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(value)) {
            isValid = false;
            message = 'Please enter a valid mobile number';
        }
    }
    
    if (!isValid) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = message;
    }
    
    return isValid;
}

function setupValidation() {
    document.querySelectorAll('input, select, textarea').forEach(field => {
        field.addEventListener('blur', () => validateField(field));
        field.addEventListener('input', () => {
            if (field.classList.contains('is-invalid')) {
                validateField(field);
            }
        });
    });
}

function setupMobileFormatting() {
    const mobileInput = document.getElementById('client_mobile');
    if (mobileInput) {
        mobileInput.addEventListener('input', function(e) {
            let value = e.target.value;

            // Remove all non-digit characters except +
            let cleaned = value.replace(/[^\d+]/g, '');

            // If it doesn't start with +91, ensure it does
            if (!cleaned.startsWith('+91')) {
                cleaned = '+91' + cleaned.replace(/^\+?91?/, '');
            }

            // Format as +91 XXXXX XXXXX
            if (cleaned.length > 3) {
                let formatted = '+91';
                let digits = cleaned.substring(3);

                if (digits.length > 0) {
                    formatted += ' ' + digits.substring(0, 5);
                }
                if (digits.length > 5) {
                    formatted += ' ' + digits.substring(5, 10);
                }

                e.target.value = formatted;
            }
        });

        // Prevent deletion of +91 prefix
        mobileInput.addEventListener('keydown', function(e) {
            const cursorPos = e.target.selectionStart;
            if ((e.key === 'Backspace' || e.key === 'Delete') && cursorPos <= 3) {
                e.preventDefault();
            }
        });

        // Clear validation error when user starts typing
        mobileInput.addEventListener('input', function(e) {
            if (e.target.classList.contains('is-invalid')) {
                e.target.classList.remove('is-invalid');
                const feedback = e.target.parentNode.querySelector('.invalid-feedback');
                if (feedback) feedback.textContent = '';
            }
        });
    }
}

function calculateCompletionDate() {
    // This function is now handled by calculateProjectDuration()
    // which updates the duration summary section
    calculateProjectDuration();
}

// Phase date listeners removed since phase targets are no longer used

// Phase date suggestion removed since phase targets are no longer used

// Phase timeline removed since phase targets are no longer used

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
}

// Removed old loadUsers function - now handled by loadUsersForSelect in setupTaskAssignments

// Form validation
function validateStep(step) {
    const stepElement = document.querySelector(`[data-step="${step}"]`);
    const requiredFields = stepElement.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        const value = field.value.trim();
        let fieldValid = true;
        let message = '';

        // Clear previous validation
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';

        if (!value) {
            fieldValid = false;
            message = 'This field is required';
        } else if (field.name === 'project_id' && value) {
            // Alphanumeric validation for project ID (matching controller pattern)
            if (!/^[a-zA-Z0-9\-_.]+$/.test(value) || value.length < 1) {
                fieldValid = false;
                message = 'Project ID can only contain letters, numbers, hyphens (-), underscores (_), and periods (.)';
            }
        }

        if (!fieldValid) {
            isValid = false;
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
        }
    });

    // Validate optional fields with specific rules
    const optionalFields = stepElement.querySelectorAll('input:not([required]), select:not([required]), textarea:not([required])');

    optionalFields.forEach(field => {
        const value = field.value.trim();
        let fieldValid = true;
        let message = '';

        // Clear previous validation
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';

        // Only validate if field has a value and it's not just the default prefix
        if (value && field.name === 'client_mobile' && value.trim() !== '+91') {
            // Check for Indian format (+91 98765 43210) or general international format
            if (!/^\+91\s[0-9]{5}\s[0-9]{5}$/.test(value) && !/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(value)) {
                fieldValid = false;
                message = 'Please enter a valid mobile number (e.g., +91 98765 43210)';
            }
        }

        if (!fieldValid) {
            isValid = false;
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
        }
    });

    // Special validation for step 2 (assignments)
    if (step === 2) {
        const assigneeSelects = stepElement.querySelectorAll('.assignee-select');
        let hasValidAssignment = false;

        assigneeSelects.forEach(select => {
            if (select.value && select.value.trim() !== '') {
                hasValidAssignment = true;
            }
        });

        if (!hasValidAssignment) {
            isValid = false;
            showAlert('error', 'At least one task assignment is required');
        }
    }

    return isValid;
}

// Real-time project ID availability check
function checkProjectIdAvailability(projectId, fieldElement) {
    if (!projectId || projectId.length < 1) return;

    // Debounce the API call
    clearTimeout(window.projectIdCheckTimeout);
    window.projectIdCheckTimeout = setTimeout(() => {
        fetch(`/projects/checkProjectId?project_id=${encodeURIComponent(projectId)}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const feedback = fieldElement.parentNode.querySelector('.invalid-feedback');
            const validFeedback = fieldElement.parentNode.querySelector('.valid-feedback');

            if (data.success) {
                if (data.exists) {
                    // Project ID already exists
                    fieldElement.classList.add('is-invalid');
                    fieldElement.classList.remove('is-valid');
                    if (feedback) feedback.textContent = 'Project ID already exists. Please choose a different ID.';
                    if (validFeedback) validFeedback.style.display = 'none';
                } else {
                    // Project ID is available
                    fieldElement.classList.remove('is-invalid');
                    fieldElement.classList.add('is-valid');
                    if (feedback) feedback.textContent = '';
                    if (validFeedback) {
                        validFeedback.textContent = 'Project ID is available';
                        validFeedback.style.display = 'block';
                    }
                }
            } else {
                // Validation error (non-numeric)
                fieldElement.classList.add('is-invalid');
                fieldElement.classList.remove('is-valid');
                if (feedback) feedback.textContent = data.message || 'Invalid project ID format';
                if (validFeedback) validFeedback.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error checking project ID:', error);
            // Don't show error to user for network issues
        });
    }, 500); // 500ms debounce
}

// Task assignment functionality
let assignmentCounter = 0;
let tasksByCategory = {}; // Will be loaded dynamically from task manager

// Load task data from task manager
function loadTaskData() {
    console.log('Loading task data from task manager');

    fetch('/task-manager/getTaskData', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Task data response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Task data received:', data);
        if (data.success && data.tasks) {
            tasksByCategory = data.tasks;
            console.log('Task categories loaded:', Object.keys(tasksByCategory));

            // Update any existing category selects
            const categorySelects = document.querySelectorAll('.task-category');
            categorySelects.forEach(select => {
                updateCategoryOptions(select);
            });
        } else {
            console.error('Failed to load task data:', data.message);
            // Fallback to hardcoded data
            tasksByCategory = {
                office: [
                    { id: 'planning', name: 'Project Planning', default_days: 3 },
                    { id: 'documentation', name: 'Documentation', default_days: 2 },
                    { id: 'permits', name: 'Permit Processing', default_days: 7 },
                    { id: 'coordination', name: 'Team Coordination', default_days: 1 }
                ],
                site: [
                    { id: 'supervision', name: 'Site Supervision', default_days: 10 },
                    { id: 'quality_control', name: 'Quality Control', default_days: 5 },
                    { id: 'safety', name: 'Safety Management', default_days: 3 },
                    { id: 'material_management', name: 'Material Management', default_days: 2 }
                ],
                design: [
                    { id: '3d_design', name: '3D Design', default_days: 14 },
                    { id: 'architectural', name: 'Architectural Design', default_days: 21 },
                    { id: 'structural', name: 'Structural Design', default_days: 14 },
                    { id: 'electrical', name: 'Electrical Design', default_days: 7 }
                ],
                management: [
                    { id: 'project_management', name: 'Project Management', default_days: 5 },
                    { id: 'client_relations', name: 'Client Relations', default_days: 2 },
                    { id: 'budget_control', name: 'Budget Control', default_days: 3 },
                    { id: 'timeline_management', name: 'Timeline Management', default_days: 2 }
                ]
            };
            console.log('Using fallback task data');
        }
    })
    .catch(error => {
        console.error('Error loading task data:', error);
        // Use fallback data
        tasksByCategory = {
            office: [
                { id: 'planning', name: 'Project Planning', default_days: 3 },
                { id: 'documentation', name: 'Documentation', default_days: 2 },
                { id: 'permits', name: 'Permit Processing', default_days: 7 },
                { id: 'coordination', name: 'Team Coordination', default_days: 1 }
            ],
            site: [
                { id: 'supervision', name: 'Site Supervision', default_days: 10 },
                { id: 'quality_control', name: 'Quality Control', default_days: 5 },
                { id: 'safety', name: 'Safety Management', default_days: 3 },
                { id: 'material_management', name: 'Material Management', default_days: 2 }
            ],
            design: [
                { id: '3d_design', name: '3D Design', default_days: 14 },
                { id: 'architectural', name: 'Architectural Design', default_days: 21 },
                { id: 'structural', name: 'Structural Design', default_days: 14 },
                { id: 'electrical', name: 'Electrical Design', default_days: 7 }
            ],
            management: [
                { id: 'project_management', name: 'Project Management', default_days: 5 },
                { id: 'client_relations', name: 'Client Relations', default_days: 2 },
                { id: 'budget_control', name: 'Budget Control', default_days: 3 },
                { id: 'timeline_management', name: 'Timeline Management', default_days: 2 }
            ]
        };
        console.log('Using fallback task data due to error');
    });
}

function setupTaskAssignments() {
    console.log('Setting up task assignments');

    // Add assignment button
    const addBtn = document.getElementById('add-assignment');
    if (addBtn) {
        console.log('Add assignment button found, adding event listener');
        addBtn.addEventListener('click', addAssignment);
    } else {
        console.error('Add assignment button not found');
    }

    // Setup initial assignment
    const initialRow = document.querySelector('.assignment-row');
    if (initialRow) {
        console.log('Initial assignment row found, setting up');
        setupAssignmentRow(initialRow);
    } else {
        console.error('Initial assignment row not found');
    }

    // Also setup any existing assignment rows
    const allRows = document.querySelectorAll('.assignment-row');
    console.log(`Found ${allRows.length} assignment rows to setup`);
    allRows.forEach((row, index) => {
        console.log(`Setting up assignment row ${index + 1}`);
        setupAssignmentRow(row);
    });
}

function setupAssignmentRow(row) {
    if (!row) {
        console.error('Assignment row is null or undefined');
        return;
    }

    console.log('Setting up assignment row:', row);

    const categorySelect = row.querySelector('.task-category');
    const taskSelect = row.querySelector('.task-type');
    const assigneeSelect = row.querySelector('.assignee-select');
    const targetDaysInput = row.querySelector('.target-days');
    const dependsOnSelect = row.querySelector('.depends-on');
    const removeBtn = row.querySelector('.remove-assignment');
    const titleElement = row.querySelector('.assignment-title');

    console.log('Assignment row elements found:', {
        categorySelect: !!categorySelect,
        taskSelect: !!taskSelect,
        assigneeSelect: !!assigneeSelect,
        targetDaysInput: !!targetDaysInput,
        dependsOnSelect: !!dependsOnSelect,
        removeBtn: !!removeBtn,
        titleElement: !!titleElement
    });

    // Check if all required elements exist
    if (!categorySelect || !taskSelect || !assigneeSelect) {
        console.error('Required assignment row elements not found:', {
            categorySelect: !!categorySelect,
            taskSelect: !!taskSelect,
            assigneeSelect: !!assigneeSelect
        });
        return;
    }

    // Update category options if tasksByCategory is loaded
    if (Object.keys(tasksByCategory).length > 0) {
        updateCategoryOptions(categorySelect);
    }

    // Category change handler
    categorySelect.addEventListener('change', function() {
        updateTaskOptions(this.value, taskSelect);
        updateDependencyOptions();
        updateTaskTitle(row);
    });

    // Task change handler
    taskSelect.addEventListener('change', function() {
        updateTargetDaysFromTask(this);
        updateDependencyOptions();
        updateTaskTitle(row);
    });

    // Target days change handler
    if (targetDaysInput) {
        targetDaysInput.addEventListener('input', function() {
            calculateProjectDuration();
        });
    }

    // Dependency change handler
    if (dependsOnSelect) {
        dependsOnSelect.addEventListener('change', function() {
            calculateProjectDuration();
        });
    }

    // Remove button handler
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            removeAssignment(row);
        });
    }

    // Load users for this assignee select
    if (assigneeSelect) {
        console.log('Loading users for assignee select in this row');
        loadUsersForSelect(assigneeSelect);
    } else {
        console.error('Assignee select not found in this row');
    }
}

// Update task title based on selected task
function updateTaskTitle(row) {
    const taskSelect = row.querySelector('.task-type');
    const titleElement = row.querySelector('.assignment-title');
    const taskNumber = Array.from(document.querySelectorAll('.assignment-row')).indexOf(row) + 1;

    if (taskSelect.value && taskSelect.selectedOptions[0]) {
        const taskName = taskSelect.selectedOptions[0].textContent;
        titleElement.innerHTML = `
            <i class="fas fa-tasks me-2"></i>
            ${taskName}
        `;
    } else {
        titleElement.innerHTML = `
            <i class="fas fa-tasks me-2"></i>
            Task #${taskNumber}
        `;
    }
}

function updateCategoryOptions(categorySelect) {
    if (!categorySelect) {
        console.error('Category select element is null or undefined');
        return;
    }

    console.log('Updating category options');

    // Clear existing options except the first one
    categorySelect.innerHTML = '<option value="">Select Category</option>';

    // Add categories from tasksByCategory
    Object.keys(tasksByCategory).forEach(categoryId => {
        const option = document.createElement('option');
        option.value = categoryId;
        option.textContent = categoryId.charAt(0).toUpperCase() + categoryId.slice(1);
        categorySelect.appendChild(option);
    });

    console.log(`Added ${Object.keys(tasksByCategory).length} categories to select`);
}

function updateTaskOptions(category, taskSelect) {
    if (!taskSelect) {
        console.error('Task select element is null or undefined');
        return;
    }

    console.log('Updating task options for category:', category);
    console.log('Available task categories:', Object.keys(tasksByCategory));
    console.log('tasksByCategory object:', tasksByCategory);

    taskSelect.innerHTML = '<option value="">Select Task</option>';

    if (category && tasksByCategory[category]) {
        console.log('Found tasks for category:', tasksByCategory[category]);
        tasksByCategory[category].forEach((task, index) => {
            console.log(`Adding task ${index + 1}:`, task);
            const option = document.createElement('option');
            option.value = task.id;
            option.textContent = task.name;
            option.dataset.defaultDays = task.default_days || 7;
            taskSelect.appendChild(option);
        });
        console.log(`Added ${tasksByCategory[category].length} tasks to select`);
        console.log('Task select final innerHTML:', taskSelect.innerHTML);

        // Auto-update target days if a task is selected
        if (taskSelect.value) {
            updateTargetDaysFromTask(taskSelect);
        }
    } else {
        console.log('No tasks found for category:', category);
        console.log('Category exists in tasksByCategory:', category in tasksByCategory);
    }
}

function updateTargetDaysFromTask(taskSelect) {
    const selectedOption = taskSelect.selectedOptions[0];
    if (selectedOption && selectedOption.dataset.defaultDays) {
        const row = taskSelect.closest('.assignment-row');
        const targetDaysInput = row?.querySelector('.target-days');
        if (targetDaysInput) {
            targetDaysInput.value = selectedOption.dataset.defaultDays;
            console.log(`Updated target days to ${selectedOption.dataset.defaultDays} for task ${selectedOption.textContent}`);
            calculateProjectDuration();
        }
    }
}

function addAssignment() {
    assignmentCounter++;
    const container = document.getElementById('task-assignments');

    const newRow = document.createElement('div');
    newRow.className = 'assignment-row';
    newRow.dataset.assignment = assignmentCounter;

    newRow.innerHTML = `
        <div class="assignment-card">
            <div class="assignment-header">
                <h6 class="assignment-title">
                    <i class="fas fa-tasks me-2"></i>
                    Task #${assignmentCounter + 1}
                </h6>
                <button type="button" class="btn btn-xs btn-outline-danger remove-assignment">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Task Category</label>
                        <select class="form-control-compact task-category" name="task_category[]">
                            <option value="">Select Category</option>
                            <option value="office">Office</option>
                            <option value="site">Site</option>
                            <option value="design">Design</option>
                            <option value="management">Management</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Task</label>
                        <select class="form-control-compact task-type" name="task_type[]">
                            <option value="">Select Task</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Assignee</label>
                        <select class="form-control-compact assignee-select" name="assigned_to[]">
                            <option value="">Loading users...</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Target Days</label>
                        <input type="number" class="form-control-compact target-days" name="target_days[]"
                               min="1" max="365" value="7">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Depends On</label>
                        <select class="form-control-compact depends-on" name="depends_on[]" multiple>
                            <option value="">No dependency (can start immediately)</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group-compact">
                        <label class="form-label-compact">Priority</label>
                        <select class="form-control-compact task-priority" name="task_priority[]">
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.appendChild(newRow);
    setupAssignmentRow(newRow);
    updateDependencyOptions();
    calculateProjectDuration();
    updateRemoveButtons();
}

// Update dependency options for all tasks
function updateDependencyOptions() {
    const allRows = document.querySelectorAll('.assignment-row');

    allRows.forEach((currentRow, currentIndex) => {
        const dependsOnSelect = currentRow.querySelector('.depends-on');
        const currentTaskName = currentRow.querySelector('.task-type option:checked')?.textContent || `Task #${currentIndex + 1}`;

        if (!dependsOnSelect) return;

        // Store current selections (for multiple select)
        const currentValues = Array.from(dependsOnSelect.selectedOptions).map(option => option.value);

        // Clear existing options except the first one
        dependsOnSelect.innerHTML = '<option value="">No dependency (can start immediately)</option>';

        // Add options for all other tasks (not just previous ones to allow flexible dependencies)
        allRows.forEach((otherRow, otherIndex) => {
            if (otherIndex !== currentIndex) { // Exclude current task
                const otherTaskName = otherRow.querySelector('.task-type option:checked')?.textContent || `Task #${otherIndex + 1}`;
                const option = document.createElement('option');
                option.value = otherIndex;
                option.textContent = `${otherTaskName} (Task #${otherIndex + 1})`;
                dependsOnSelect.appendChild(option);
            }
        });

        // Restore previous selections if still valid
        currentValues.forEach(value => {
            if (value && dependsOnSelect.querySelector(`option[value="${value}"]`)) {
                const option = dependsOnSelect.querySelector(`option[value="${value}"]`);
                if (option) {
                    option.selected = true;
                }
            }
        });
    });
}

// Calculate project duration based on dependencies and target days
function calculateProjectDuration() {
    const allRows = document.querySelectorAll('.assignment-row');
    let totalDays = 0;
    let criticalPathDays = 0;

    // Simple calculation: sum all target days for total, max path for critical
    const taskData = [];

    allRows.forEach((row, index) => {
        const targetDays = parseInt(row.querySelector('.target-days')?.value || 7);
        const dependsOn = row.querySelector('.depends-on')?.value;

        taskData.push({
            index: index,
            days: targetDays,
            dependsOn: dependsOn ? parseInt(dependsOn) : null
        });

        totalDays += targetDays;
    });

    // Calculate critical path (longest dependency chain)
    criticalPathDays = calculateCriticalPath(taskData);

    // Update duration display
    document.getElementById('totalDays').textContent = totalDays;
    document.getElementById('criticalPath').textContent = criticalPathDays;

    // Calculate estimated completion date
    const startDate = document.getElementById('start_date')?.value;
    if (startDate) {
        const completionDate = new Date(startDate);
        completionDate.setDate(completionDate.getDate() + criticalPathDays);
        document.getElementById('estimatedCompletion').textContent = completionDate.toLocaleDateString();
    }
}

// Calculate critical path duration
function calculateCriticalPath(tasks) {
    if (tasks.length === 0) return 0;

    const memo = {};

    function getTaskDuration(taskIndex) {
        if (memo[taskIndex] !== undefined) {
            return memo[taskIndex];
        }

        const task = tasks[taskIndex];
        if (!task) return 0;

        let maxDependencyDuration = 0;

        if (task.dependsOn !== null && task.dependsOn < taskIndex) {
            maxDependencyDuration = getTaskDuration(task.dependsOn);
        }

        memo[taskIndex] = task.days + maxDependencyDuration;
        return memo[taskIndex];
    }

    let maxDuration = 0;
    tasks.forEach((task, index) => {
        const duration = getTaskDuration(index);
        maxDuration = Math.max(maxDuration, duration);
    });

    return maxDuration;
}

function removeAssignment(row) {
    row.remove();
    updateDependencyOptions();
    calculateProjectDuration();
    updateRemoveButtons();
}

function updateRemoveButtons() {
    const rows = document.querySelectorAll('.assignment-row');
    rows.forEach((row, index) => {
        const removeBtn = row.querySelector('.remove-assignment');
        removeBtn.style.display = rows.length > 1 ? 'block' : 'none';
    });
}

function loadUsersForSelect(selectElement) {
    if (!selectElement) {
        console.error('Select element is null or undefined');
        return;
    }

    console.log('Loading users for select element:', selectElement);
    console.log('Select element classes:', selectElement.className);
    console.log('Select element current innerHTML:', selectElement.innerHTML);

    // Set loading state
    selectElement.innerHTML = '<option value="">Loading users...</option>';

    fetch('/projects/getUsers', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            console.log('Users fetch response status:', response.status);
            console.log('Users fetch response headers:', response.headers);

            if (response.status === 401) {
                // Authentication required - redirect to login
                console.warn('Authentication required for getUsers endpoint');
                selectElement.innerHTML = '<option value="">Please log in to load users</option>';
                // Optionally redirect to login page
                if (confirm('You need to log in to access this feature. Redirect to login page?')) {
                    window.location.href = '/auth/login';
                }
                return null;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data) return; // Handle authentication redirect case

            console.log('Users data received:', data);
            selectElement.innerHTML = '<option value="">Select a user...</option>';

            if (data.success && data.users && Array.isArray(data.users)) {
                console.log(`Processing ${data.users.length} users`);
                data.users.forEach((user, index) => {
                    console.log(`Adding user ${index + 1}:`, user);
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.username} (${user.roles})`;
                    option.dataset.username = user.username;
                    option.dataset.roles = user.roles;
                    selectElement.appendChild(option);
                });

                console.log(`Successfully loaded ${data.users.length} users into select`);
                console.log('Final select innerHTML:', selectElement.innerHTML);

                // Initialize Select2 for this select if jQuery and Select2 are available
                if (typeof $ !== 'undefined' && $.fn.select2) {
                    console.log('Initializing Select2 for this select');
                    $(selectElement).select2({
                        placeholder: 'Search and select an assignee...',
                        allowClear: true,
                        width: '100%',
                        theme: 'bootstrap-5'
                    });
                } else {
                    console.log('jQuery or Select2 not available');
                }
            } else if (data.success === false && data.message) {
                console.error('Server error:', data.message);
                selectElement.innerHTML = `<option value="">Error: ${data.message}</option>`;
            } else {
                console.error('Invalid users data structure:', data);
                selectElement.innerHTML = '<option value="">Error: Invalid data format</option>';
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            selectElement.innerHTML = '<option value="">Network error - please try again</option>';
        });
}

function handleSubmit(e) {
    e.preventDefault();
    
    if (!validateCurrentStep()) {
        showAlert('error', 'Please fill in all required fields correctly');
        return;
    }
    
    const submitBtn = document.getElementById('submit-btn');
    const spinner = submitBtn.querySelector('.spinner-border');
    
    // Show loading state
    submitBtn.disabled = true;
    spinner.style.display = 'inline-block';
    
    const formData = new FormData(e.target);

    // Debug: Log all form data
    console.log('Form data being sent:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // CSRF token is already included via csrf_field() in the form

    fetch(`${window.location.origin}/projects/create`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.success) {
            // Show success modal
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        } else {
            console.error('Project creation failed:', data);
            showAlert('error', data.message || 'Failed to create project');

            // Show debug info if available
            if (data.debug_info) {
                console.error('Debug info:', data.debug_info);
            }

            // Show validation errors if any
            if (data.errors) {
                console.log('Validation errors:', data.errors);
                Object.keys(data.errors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        const feedback = input.parentNode.querySelector('.invalid-feedback');
                        if (feedback) feedback.textContent = data.errors[field];
                    }
                });
            }
        }
    })
    .catch(error => {
        console.error('Error creating project:', error);
        showAlert('error', `Network error: ${error.message}. Please check the console for details.`);
    })
    .finally(() => {
        submitBtn.disabled = false;
        spinner.style.display = 'none';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<?= $this->endSection() ?>
