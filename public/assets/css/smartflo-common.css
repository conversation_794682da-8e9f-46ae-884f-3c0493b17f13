/* ===== SMARTFLO COMMON STYLES - DASHBOARD DESIGN ===== */

/* ===== MODERN CSS VARIABLES ===== */
:root {
    /* Colors - Modern Palette */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary: #8b5cf6;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Neutrals - Refined Grays */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Layout */
    --sidebar-width: 280px;
    --sidebar-mini-width: 72px;
    --header-height: 72px;
    --bottom-nav-height: 55px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== GLOBAL RESET ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* ===== LAYOUT STRUCTURE ===== */
.app-container {
    display: flex;
    min-height: 100vh;
    background: var(--gray-50);
}

/* ===== SIDEBAR ===== */
.sidebar {
    width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--gray-200);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform var(--transition-base);
    overflow-y: auto;
}

.sidebar-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--gray-100);
}

/* Compact header for mini sidebar */
.sidebar.mini .sidebar-header {
    padding: var(--space-sm); /* Reduced padding for mini */
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    text-decoration: none;
    color: var(--gray-900);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.sidebar-nav {
    padding: var(--space-sm) 0;
}

/* Compact nav for mini sidebar */
.sidebar.mini .sidebar-nav {
    padding: var(--space-xs) 0; /* Reduced padding for mini */
}

.nav-section {
    margin-bottom: 0.5rem;
}

.nav-section-title {
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-500);
    padding: 0 var(--space-md);
    margin-bottom: 0.125rem;
}

.nav-item {
    margin: 0.0625rem var(--space-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: 0.4rem var(--space-md) !important; /* More compact padding */
    border-radius: 6px;
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
    font-size: 0.8rem; /* Smaller font for compactness */
    min-height: 32px; /* Reduced height */
    box-sizing: border-box;
}

.nav-link:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.nav-link.active {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 16px;
    text-align: center;
    font-size: 0.95rem;
    flex-shrink: 0;
    color: inherit;
    opacity: 0.8;
}

.nav-link:hover i,
.nav-link.active i {
    opacity: 1;
}

/* ===== SIDEBAR TOGGLE ===== */
.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: -12px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: var(--primary);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition-base);
    z-index: 1001;
    box-shadow: var(--shadow-md);
}

.sidebar-toggle:hover {
    background: var(--primary-dark);
    transform: translateY(-50%) scale(1.1);
}

/* ===== MINI SIDEBAR WITH HOVER EXPAND - REDESIGNED ===== */
.sidebar.mini {
    width: var(--sidebar-mini-width);
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: visible;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
}

/* Hover to expand - overlay mode */
.sidebar.mini:hover,
.sidebar.mini.hover-expanded {
    width: var(--sidebar-width);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    background: var(--white) !important;
    border-right: 1px solid var(--gray-200);
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    backdrop-filter: none;
}

/* Add backdrop overlay when sidebar is expanded */
.sidebar.mini:hover::before,
.sidebar.mini.hover-expanded::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
    pointer-events: none;
}

.sidebar.mini .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.mini:hover .logo-text,
.sidebar.mini.hover-expanded .logo-text {
    opacity: 1;
    width: auto;
}

.sidebar.mini .nav-section-title {
    opacity: 0;
    height: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.mini:hover .nav-section-title,
.sidebar.mini.hover-expanded .nav-section-title {
    opacity: 1;
    height: auto;
    margin: 0.75rem 0 0.25rem 0;
    padding: 0 var(--space-xl);
}

.sidebar.mini .nav-link {
    justify-content: center;
    padding: 0.5rem !important;
    position: relative;
    border-radius: 8px;
    margin: 0.125rem 0.25rem;
    min-height: 40px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.sidebar.mini:hover .nav-link,
.sidebar.mini.hover-expanded .nav-link {
    justify-content: flex-start;
    padding: 0.5rem 1rem !important;
}

.sidebar.mini .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 0;
}

.sidebar.mini:hover .nav-link span,
.sidebar.mini.hover-expanded .nav-link span {
    opacity: 1;
    width: auto;
    margin-left: 0.75rem;
}

.sidebar.mini .nav-link i {
    min-width: 16px;
    text-align: center;
    font-size: 1rem;
    flex-shrink: 0;
    opacity: 1;
    color: var(--gray-700);
}

.sidebar.mini .nav-link:hover i {
    color: var(--primary);
    transform: scale(1.1);
}

.sidebar.mini .nav-link.active i {
    color: white;
}

.sidebar.mini .sidebar-toggle i {
    transform: rotate(180deg);
}

/* Tooltip for mini mode */
.sidebar.mini .nav-link:not(.hover-expanded):hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dark);
    color: var(--white);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1060;
    margin-left: 0.5rem;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease forwards;
    pointer-events: none;
}

@keyframes tooltipFadeIn {
    from { opacity: 0; transform: translateY(-50%) translateX(-5px); }
    to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

/* ===== MAIN CONTENT ADJUSTMENT ===== */
.main-content.sidebar-mini {
    margin-left: var(--sidebar-mini-width);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--gray-50);
}

/* ===== HEADER ===== */
.header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 0 var(--space-xl);
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1,
.page-title-mobile h1,
.page-title-desktop h1 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.page-title-mobile {
    flex: 1;
    text-align: center;
}

.page-title-desktop {
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.header-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.header-btn:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: var(--error);
    color: white;
    border-radius: 50%;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    background: var(--gray-50);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-menu:hover {
    background: var(--gray-100);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.8rem;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "kern" 1;
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* ===== CONTENT AREA ===== */
.content {
    padding: var(--space-xl);
}

/* ===== MOBILE HEADER ===== */
.mobile-header {
    display: none;
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-md) var(--space-lg);
    height: var(--header-height);
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
}

.mobile-header .logo {
    gap: var(--space-sm);
}

.mobile-header .logo-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
}

.mobile-header .logo-text {
    font-size: 1.25rem;
}

.mobile-actions {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

/* ===== BOTTOM NAVIGATION ===== */
.bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-top: 1px solid #e2e8f0;
    padding: 0.2rem 0;
    z-index: 1000;
    height: var(--bottom-nav-height);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.bottom-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
    text-decoration: none;
    color: #64748b;
    font-size: 0.6rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 45px;
    min-height: 45px;
    justify-content: center;
    border-radius: 8px;
    text-align: center;
    padding: 0.2rem 0.4rem;
}

.bottom-nav-item:hover,
.bottom-nav-item.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.bottom-nav-item i {
    font-size: 1rem;
    margin-bottom: 0.1rem;
}

.bottom-nav-item span {
    line-height: 1;
    white-space: nowrap;
}

/* ===== CARDS ===== */
.card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--space-lg);
    overflow: hidden;
}

.card-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-lg);
}

.card-body {
    padding: var(--space-lg);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

/* ===== BUTTONS ===== */
.btn {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 0.875rem;
    min-height: 44px;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-outline-secondary:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-success:hover {
    background: #059669;
    color: white;
}

.btn-warning {
    background: var(--warning);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
    color: white;
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    min-height: 36px;
}

/* ===== FORMS ===== */
.form-control {
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-sm);
    display: block;
}

/* ===== TABLES ===== */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--gray-700);
    background: var(--gray-50);
    padding: var(--space-md);
    text-align: left;
}

.table td {
    padding: var(--space-md);
    border-top: 1px solid var(--gray-200);
}

/* ===== BADGES ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.badge-primary {
    background: var(--primary);
    color: white;
}

.badge-success {
    background: var(--success);
    color: white;
}

.badge-warning {
    background: var(--warning);
    color: white;
}

.badge-danger {
    background: var(--error);
    color: white;
}

/* ===== ALERTS ===== */
.alert {
    border-radius: var(--radius-md);
    border: none;
    padding: var(--space-md) var(--space-lg);
    margin-bottom: var(--space-md);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border-left: 4px solid var(--error);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border-left: 4px solid var(--warning);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border-left: 4px solid var(--info);
}

/* ===== USER MANAGEMENT STYLES ===== */
.user-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.user-card-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.user-card .user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.125rem;
}

.user-info h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.user-info p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.user-card-body {
    margin-bottom: var(--space-lg);
}

.user-roles {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
    margin-bottom: var(--space-md);
}

.role-badge {
    background: var(--primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.user-status {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 0.875rem;
}

.status-active {
    color: var(--success);
}

.status-inactive {
    color: var(--error);
}

.user-card-actions {
    display: flex;
    gap: var(--space-sm);
    justify-content: flex-end;
}

.user-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.search-container {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.search-input {
    width: 100%;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== PROFESSIONAL MINIMAL NOTIFICATION TRAY ===== */
.notification-tray {
    position: fixed;
    top: 70px;
    right: 20px;
    width: 300px;
    max-height: 350px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1050;
    transform: translateY(-8px) scale(0.98);
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    backdrop-filter: blur(8px);
}

.notification-tray.show {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.notification-tray-header {
    padding: 16px 20px 12px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.notification-tray-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #334155;
    margin: 0;
    letter-spacing: -0.01em;
}

.notification-tray-close {
    background: rgba(148, 163, 184, 0.1);
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    font-size: 0.75rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.notification-tray-close:hover {
    background: rgba(148, 163, 184, 0.2);
    color: #475569;
    transform: scale(1.05);
}

.notification-tray-list {
    max-height: 280px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.notification-tray-list::-webkit-scrollbar {
    width: 4px;
}

.notification-tray-list::-webkit-scrollbar-track {
    background: transparent;
}

.notification-tray-list::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 2px;
}

.notification-item {
    padding: 14px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.notification-item:hover {
    background: rgba(59, 130, 246, 0.02);
    transform: translateX(2px);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.04) 0%, rgba(59, 130, 246, 0.01) 100%);
    border-left: 3px solid #3b82f6;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 16px;
    right: 16px;
    width: 6px;
    height: 6px;
    background: #3b82f6;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.notification-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.85rem;
    margin-bottom: 6px;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.notification-message {
    color: #64748b;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 8px;
}

.notification-time {
    color: #94a3b8;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    gap: 4px;
}

.notification-time i {
    font-size: 0.65rem;
}

.notification-empty {
    padding: 32px 20px;
    text-align: center;
    color: #94a3b8;
}

.notification-empty i {
    font-size: 1.5rem;
    margin-bottom: 12px;
    display: block;
    color: #cbd5e1;
}

.notification-empty p {
    font-size: 0.8rem;
    margin: 0;
    color: #64748b;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .notification-tray {
        right: 10px;
        width: 280px;
        max-height: 320px;
    }

    .notification-tray-header {
        padding: 14px 16px 10px 16px;
    }

    .notification-item {
        padding: 12px 16px;
    }
}

.notification-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.notification-header h5 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
}

.notification-list {
    padding: var(--space-md);
}

.notification-item {
    display: flex;
    gap: var(--space-md);
    padding: var(--space-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-sm);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.notification-text {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.notification-time {
    color: var(--gray-500);
    font-size: 0.75rem;
}

.notification-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--gray-200);
    text-align: center;
    background: var(--gray-50);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991px) {
    .notification-tray {
        width: 100%;
        right: -100%;
    }

    .notification-tray.show {
        right: 0;
    }
    .sidebar {
        transform: translateX(-100%);
        z-index: 1002; /* Higher than header */
    }

    .sidebar.show {
        transform: translateX(0);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    }

    .main-content {
        margin-left: 0;
        padding-top: var(--header-height);
        padding-bottom: var(--bottom-nav-height);
    }

    /* Ensure header is always visible on mobile */
    .mobile-header {
        display: none !important;
    }

    .header {
        display: flex !important;
        padding: 0 var(--space-md) !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1001 !important;
        background: white !important;
        border-bottom: 1px solid var(--gray-200) !important;
        height: var(--header-height) !important;
        align-items: center !important;
        justify-content: space-between !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    /* Ensure header-left is visible */
    .header-left {
        display: block !important;
        flex: 1 !important;
    }

    .header-left h1 {
        font-size: 1.125rem !important;
        margin: 0 !important;
        color: var(--gray-900) !important;
        font-weight: 600 !important;
    }

    .header-right {
        display: flex !important;
        gap: var(--space-sm) !important;
        align-items: center !important;
    }

    .header-btn {
        width: 40px;
        height: 40px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-md);
        background: transparent;
        border: none;
        color: var(--gray-600);
        transition: all var(--transition-fast);
    }

    .header-btn:hover {
        background: var(--gray-100);
        color: var(--gray-900);
    }

    .user-menu {
        padding: var(--space-xs) var(--space-sm);
    }

    .user-info {
        display: none; /* Hide user info text on mobile */
    }

    /* Ensure user-name is properly sized on all devices */
    .user-name {
        font-size: 0.75rem !important;
        max-width: 100px !important;
    }

    .bottom-nav {
        display: block;
    }

    /* Adjust dropdown positioning for mobile */
    .dropdown-tray {
        width: calc(100vw - 20px);
        max-width: 350px;
        right: 10px;
        max-height: 60vh;
        border-radius: var(--radius-lg);
    }

    .user-dropdown {
        right: 10px;
        width: calc(100vw - 20px);
        max-width: 280px;
    }

    /* Fix Bootstrap dropdown positioning on mobile */
    .dropdown-menu {
        position: absolute !important;
        top: 100% !important;
        right: 0 !important;
        left: auto !important;
        max-width: calc(100vw - 20px) !important;
        min-width: 280px !important;
        transform: none !important;
        margin-top: 5px !important;
        margin-right: 10px !important;
        border: 1px solid #dee2e6 !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        border-radius: 0.375rem !important;
        background: white !important;
        z-index: 1050 !important;
    }

    /* Specific fixes for navbar dropdowns */
    .navbar-nav .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        max-width: 90vw !important;
        width: 320px !important;
    }

    /* Ensure dropdowns don't go off screen */
    .dropdown-menu.dropdown-menu-end {
        right: 0 !important;
        left: auto !important;
    }

    /* Fix notification and message dropdown content */
    .notification-item, .message-item {
        padding: 12px 16px !important;
        border-bottom: 1px solid #f0f0f0 !important;
        word-wrap: break-word !important;
        max-width: 100% !important;
    }

    .notification-item:last-child, .message-item:last-child {
        border-bottom: none !important;
    }

    /* Touch-friendly improvements */
    .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 44px;
    }

    .btn {
        min-height: 44px; /* Touch-friendly */
        font-size: 0.9rem;
    }

    /* Modal adjustments */
    .modal-dialog {
        margin: var(--space-md);
        max-width: calc(100vw - 2rem);
    }

    /* Notification badge adjustments */
    .notification-badge {
        font-size: 0.7rem;
        min-width: 18px;
        height: 18px;
    }

    .content {
        padding: var(--space-md);
    }

    .user-cards {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .bottom-nav {
        display: none;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: var(--space-xs);
}

.mb-2 {
    margin-bottom: var(--space-sm);
}

.mb-3 {
    margin-bottom: var(--space-md);
}

.mb-4 {
    margin-bottom: var(--space-lg);
}

.mb-5 {
    margin-bottom: var(--space-xl);
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--space-sm);
}

.gap-3 {
    gap: var(--space-md);
}

/* ===== USER DROPDOWN ===== */
.user-dropdown {
    position: fixed;
    top: 70px;
    right: 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    min-width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.user-dropdown-header .user-avatar.large {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
}

.user-dropdown-menu {
    padding: var(--space-sm) 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-item.logout {
    color: var(--error);
}

.dropdown-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--space-sm) 0;
}

/* ===== WHATSAPP-STYLE READ RECEIPTS ===== */
.read-receipt {
    font-size: 0.75rem;
    margin-left: 8px;
    display: inline-flex;
    align-items: center;
}

.read-receipt.sent i {
    color: #9ca3af !important; /* Gray for sent */
}

.read-receipt.read i {
    color: #3b82f6 !important; /* Blue for read */
}

.read-receipt i {
    font-size: 12px;
}

/* Message status indicators */
.message-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
}

.status-sent {
    color: #9ca3af;
}

.status-delivered {
    color: #9ca3af;
}

.status-read {
    color: #3b82f6;
}

/* ===== PROFESSIONAL FONT SIZING ===== */
/* Base font sizes - professional and readable */
body {
    font-size: 14px;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Headers - professional sizing */
h1 {
    font-size: 1.75rem !important; /* 28px */
    font-weight: 600 !important;
    line-height: 1.2 !important;
    margin-bottom: 1rem !important;
}

h2 {
    font-size: 1.5rem !important; /* 24px */
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin-bottom: 0.875rem !important;
}

h3 {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin-bottom: 0.75rem !important;
}

h4 {
    font-size: 1.125rem !important; /* 18px */
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin-bottom: 0.625rem !important;
}

h5 {
    font-size: 1rem !important; /* 16px */
    font-weight: 600 !important;
    line-height: 1.5 !important;
    margin-bottom: 0.5rem !important;
}

h6 {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 600 !important;
    line-height: 1.5 !important;
    margin-bottom: 0.5rem !important;
}

/* Text elements */
p {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
}

/* Small text */
small, .small {
    font-size: 0.75rem !important; /* 12px */
    line-height: 1.5 !important;
}

/* Button text */
.btn {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    line-height: 1.5 !important;
    padding: 0.5rem 1rem !important;
}

.btn-sm {
    font-size: 0.75rem !important; /* 12px */
    padding: 0.375rem 0.75rem !important;
}

.btn-lg {
    font-size: 1rem !important; /* 16px */
    padding: 0.75rem 1.5rem !important;
}

/* Form elements */
.form-control, .form-select {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.5 !important;
    padding: 0.5rem 0.75rem !important;
}

.form-label {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    margin-bottom: 0.5rem !important;
}

/* Navigation */
.nav-link {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
}

/* Cards */
.card-title {
    font-size: 1.125rem !important; /* 18px */
    font-weight: 600 !important;
    margin-bottom: 0.75rem !important;
}

.card-text {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.6 !important;
}

/* Tables */
.table {
    font-size: 0.875rem !important; /* 14px */
}

.table th {
    font-weight: 600 !important;
    font-size: 0.75rem !important; /* 12px */
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
}

/* Badges */
.badge {
    font-size: 0.75rem !important; /* 12px */
    font-weight: 500 !important;
    padding: 0.25rem 0.5rem !important;
}

/* Alerts */
.alert {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.5 !important;
}

/* Dropdown items */
.dropdown-item {
    font-size: 0.875rem !important; /* 14px */
    padding: 0.5rem 1rem !important;
}

/* List group items */
.list-group-item {
    font-size: 0.875rem !important; /* 14px */
    padding: 0.75rem 1rem !important;
}

/* Modal content */
.modal-title {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 600 !important;
}

.modal-body {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.6 !important;
}



/* Header specific */
.header h1 {
    font-size: 1.5rem !important; /* 24px */
    font-weight: 700 !important;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    body {
        font-size: 14px !important;
    }

    h1 {
        font-size: 1.5rem !important; /* 24px on mobile */
    }

    h2 {
        font-size: 1.25rem !important; /* 20px on mobile */
    }

    h3 {
        font-size: 1.125rem !important; /* 18px on mobile */
    }

    .header h1 {
        font-size: 1.25rem !important; /* 20px on mobile */
    }

    .btn {
        font-size: 0.875rem !important;
        padding: 0.625rem 1rem !important;
        min-height: 44px !important; /* Touch-friendly */
    }

    .form-control, .form-select {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
    }
}