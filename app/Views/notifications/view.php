<?= $this->extend('layouts/app') ?>

<?= $this->section('title') ?>
<?= $title ?? 'View Notification - SmartFlo' ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Notification Details</h1>
                    <p class="text-muted mb-0">View notification information</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/notifications" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Notifications
                    </a>
                    <?php if (!$notification['is_read']): ?>
                    <button class="btn btn-primary" onclick="markAsRead()">
                        <i class="fas fa-check me-2"></i>
                        Mark as Read
                    </button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Notification Card -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center gap-3">
                            <div class="notification-icon">
                                <?php
                                $iconClass = 'fas fa-bell';
                                $iconColor = 'text-primary';
                                
                                switch ($notification['type']) {
                                    case 'success':
                                        $iconClass = 'fas fa-check-circle';
                                        $iconColor = 'text-success';
                                        break;
                                    case 'warning':
                                        $iconClass = 'fas fa-exclamation-triangle';
                                        $iconColor = 'text-warning';
                                        break;
                                    case 'error':
                                        $iconClass = 'fas fa-times-circle';
                                        $iconColor = 'text-danger';
                                        break;
                                    case 'info':
                                    default:
                                        $iconClass = 'fas fa-info-circle';
                                        $iconColor = 'text-info';
                                        break;
                                }
                                ?>
                                <i class="<?= $iconClass ?> <?= $iconColor ?>" style="font-size: 2rem;"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-1"><?= htmlspecialchars($notification['title']) ?></h5>
                                <div class="d-flex align-items-center gap-3 text-muted">
                                    <span>
                                        <i class="fas fa-clock me-1"></i>
                                        <?= date('M j, Y \a\t g:i A', strtotime($notification['created_at'])) ?>
                                    </span>
                                    <span class="badge bg-<?= $notification['type'] === 'error' ? 'danger' : $notification['type'] ?>">
                                        <?= ucfirst($notification['type']) ?>
                                    </span>
                                    <?php if ($notification['is_read']): ?>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-check me-1"></i>Read
                                    </span>
                                    <?php else: ?>
                                    <span class="badge bg-primary">
                                        <i class="fas fa-circle me-1"></i>Unread
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <?php if (!$notification['is_read']): ?>
                                <li><a class="dropdown-item" href="#" onclick="markAsRead()">
                                    <i class="fas fa-check me-2"></i>Mark as Read
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification()">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="notification-content">
                        <?= nl2br(htmlspecialchars($notification['message'])) ?>
                    </div>
                    
                    <!-- Action buttons if needed -->
                    <div class="notification-actions mt-4">
                        <?php if ($notification['type'] === 'warning' || $notification['type'] === 'error'): ?>
                        <div class="alert alert-<?= $notification['type'] === 'error' ? 'danger' : 'warning' ?> mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            This notification requires your attention. Please take appropriate action if needed.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Related Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/notifications" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    View All Notifications
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <button class="btn btn-outline-secondary" onclick="markAllAsRead()">
                                    <i class="fas fa-check-double me-2"></i>
                                    Mark All as Read
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary);
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: var(--gray-100);
    border-radius: 50%;
}

@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .d-flex.gap-2 {
        width: 100%;
        justify-content: stretch;
    }
    
    .d-flex.gap-2 .btn {
        flex: 1;
    }
    
    .notification-icon {
        width: 50px;
        height: 50px;
    }
    
    .notification-icon i {
        font-size: 1.5rem !important;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function markAsRead() {
    fetch('/api/mark-notification-read/<?= $notification['id'] ?>', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Notification marked as read');
            // Reload page to update UI
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message || 'Failed to mark as read');
        }
    })
    .catch(error => {
        console.error('Error marking as read:', error);
        showAlert('danger', 'Error marking notification as read');
    });
}

function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) {
        return;
    }
    
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'All notifications marked as read');
            setTimeout(() => {
                window.location.href = '/notifications';
            }, 1500);
        } else {
            showAlert('danger', data.message || 'Failed to mark all as read');
        }
    })
    .catch(error => {
        console.error('Error marking all as read:', error);
        showAlert('danger', 'Error marking all notifications as read');
    });
}

function deleteNotification() {
    if (!confirm('Are you sure you want to delete this notification? This action cannot be undone.')) {
        return;
    }
    
    fetch('/notifications/<?= $notification['id'] ?>', {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Notification deleted successfully');
            setTimeout(() => {
                window.location.href = '/notifications';
            }, 1500);
        } else {
            showAlert('danger', data.message || 'Failed to delete notification');
        }
    })
    .catch(error => {
        console.error('Error deleting notification:', error);
        showAlert('danger', 'Error deleting notification');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
<?= $this->endSection() ?>
